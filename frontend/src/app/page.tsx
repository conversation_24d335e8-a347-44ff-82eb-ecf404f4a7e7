'use client';

import { useState } from 'react';
import axios from 'axios';
import { FileText, Image as ImageIcon, Loader2, AlertCircle } from 'lucide-react';
import ArticleInput from '@/app/components/ArticleInput';
import VisualAbstract from '@/app/components/VisualAbstract';
import GeneratedAbstract from '@/app/components/GeneratedAbstract';

interface ArticleData {
  content?: string;
  visualAbstract?: string;
}

export default function Home() {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [articleData, setArticleData] = useState<ArticleData | null>(null);

  const handleSubmit = async (articleUrl: string) => {
    setLoading(true);
    setError('');
    setArticleData(null);

    try {
      const response = await axios.post('http://localhost:4567/api/process-article', {
        url: articleUrl
      }, {
        timeout: 30000 // 30 saniye timeout
      });

      // MCP'den gelen veriyi kontrol et
      if (response.data && (response.data.content || response.data.visualAbstract)) {
        setArticleData(response.data);
      } else {
        setError('Makale verisi alınamadı. URL\'yi kontrol edin.');
      }
    } catch (err: any) {
      console.error('API Error:', err);
      
      if (err.code === 'ECONNABORTED') {
        setError('İstek zaman aşımına uğradı. Lütfen tekrar deneyin.');
      } else if (err.response?.status === 400) {
        setError(err.response.data?.error || 'Geçersiz URL veya makale bulunamadı.');
      } else if (err.response?.status === 500) {
        setError('Sunucu hatası. Lütfen daha sonra tekrar deneyin.');
      } else if (err.code === 'ERR_NETWORK') {
        setError('Bağlantı hatası. Backend servisinin çalıştığından emin olun.');
      } else {
        setError(err.response?.data?.error || 'Makale işlenirken bir hata oluştu.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            JAMA Visual Abstract Generator
          </h1>
          <p className="text-lg text-gray-600">
            JAMA makalelerinden görsel abstract oluşturun
          </p>
        </div>

        {/* Input Section */}
        <div className="max-w-2xl mx-auto mb-8">
          <ArticleInput onSubmit={handleSubmit} loading={loading} />
        </div>

        {/* Error Display */}
        {error && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
              <AlertCircle className="text-red-500" size={20} />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <Loader2 className="animate-spin mx-auto mb-4" size={32} />
              <p className="text-gray-600">Makale işleniyor...</p>
              <p className="text-sm text-gray-500 mt-2">Bu işlem birkaç dakika sürebilir</p>
            </div>
          </div>
        )}

        {/* Results */}
        {articleData && !loading && (
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Original Visual Abstract */}
              {articleData.visualAbstract && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <ImageIcon className="text-blue-600" size={20} />
                    <h2 className="text-xl font-semibold text-gray-900">
                      Orijinal Görsel Abstract
                    </h2>
                  </div>
                  <VisualAbstract imageUrl={articleData.visualAbstract} />
                </div>
              )}

              {/* Generated Visual Abstract */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="text-green-600" size={20} />
                  <h2 className="text-xl font-semibold text-gray-900">
                    Oluşturulan Görsel Abstract
                  </h2>
                </div>
                <GeneratedAbstract articleContent={articleData.content} />
              </div>
            </div>

            {/* Info about missing original abstract */}
            {!articleData.visualAbstract && (
              <div className="max-w-4xl mx-auto mt-8">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-700 text-sm">
                    <strong>Not:</strong> Bu makalede orijinal görsel abstract bulunamadı. 
                    Sadece makale içeriğinden oluşturulan görsel abstract gösterilmektedir.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
