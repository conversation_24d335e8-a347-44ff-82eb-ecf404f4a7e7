'use client';

import Image from 'next/image';
import { ImageOff } from 'lucide-react';

interface VisualAbstractProps {
  imageUrl: string;
}

export default function VisualAbstract({ imageUrl }: VisualAbstractProps) {
  return (
    <div className="relative">
      {imageUrl ? (
        <div className="relative w-full h-96 bg-gray-100 rounded-lg overflow-hidden">
          <Image
            src={imageUrl}
            alt="Orijinal görsel abstract"
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
          />
        </div>
      ) : (
        <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center text-gray-500">
            <ImageOff size={48} className="mx-auto mb-2" />
            <p>Görsel abstract bulunamadı</p>
          </div>
        </div>
      )}
    </div>
  );
} 