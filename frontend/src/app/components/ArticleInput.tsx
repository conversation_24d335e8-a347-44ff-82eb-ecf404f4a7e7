'use client';

import { useState } from 'react';
import { Search, Loader2 } from 'lucide-react';

interface ArticleInputProps {
  onSubmit: (url: string) => void;
  loading: boolean;
}

export default function ArticleInput({ onSubmit, loading }: ArticleInputProps) {
  const [url, setUrl] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim()) {
      onSubmit(url.trim());
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        JAMA Makale URL'si Girin
      </h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
            Makale URL'si
          </label>
          <input
            type="url"
            id="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://jamanetwork.com/journals/jama/fullarticle/..."
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
            disabled={loading}
          />
        </div>
        <button
          type="submit"
          disabled={loading || !url.trim()}
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {loading ? (
            <>
              <Loader2 className="animate-spin" size={20} />
              İşleniyor...
            </>
          ) : (
            <>
              <Search size={20} />
              Makaleyi İşle
            </>
          )}
        </button>
      </form>
      <div className="mt-4 text-sm text-gray-600">
        <p>Desteklenen formatlar:</p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>JAMA Network makaleleri</li>
          <li>JAMA Internal Medicine makaleleri</li>
          <li>JAMA Surgery makaleleri</li>
        </ul>
      </div>
    </div>
  );
} 