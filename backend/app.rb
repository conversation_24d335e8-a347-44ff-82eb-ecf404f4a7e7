require 'sinatra'
require 'httparty'
require 'dotenv/load'
require 'json'

# Server config
set :port, 4567
set :bind, '0.0.0.0'

# CORS (geliştirme süreci iç<PERSON>ı<PERSON>)
before do
  headers 'Access-Control-Allow-Origin' => '*',
          'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers' => 'Content-Type'
end

options '*' do
  200
end

# Smithery MCP endpoint
SMITHERY_ENDPOINT = "https://server.smithery.ai/@aliemregencer/jama-abstract-mcp/mcp?api_key=052c1489-c4b2-41c1-8f17-022474b631ec&profile=agreed-newt-U9p9aj"

# JSON-RPC formatında POST istekleri için yardımcı
def json_rpc_call(method_name, url_param)
  body = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/call",
    params: {
      name: method_name,
      arguments: {
        url: url_param
      }
    }
  }

  begin
    response = HTTParty.post(SMITHERY_ENDPOINT,
      body: body.to_json,
      headers: {
        'Content-Type' => 'application/json',
        'Accept' => 'application/json, text/event-stream'
      },
      timeout: 60
    )
    return response
  rescue => e
    puts "MCP bağlantı hatası: #{e.message}"
    return nil
  end
end


# Ana API endpoint
post '/api/process-article' do
  content_type :json

  begin
    payload = JSON.parse(request.body.read)
    url = payload['url']
    halt 400, { error: 'URL gerekli' }.to_json unless url

    result = {
      content: nil,
      visualAbstract: nil
    }

    # Test için mock data kontrolü
    if url.include?("test")
      result[:content] = "This is a test article content for demonstration purposes. The study included 500 participants with a mean age of 60 years. The intervention group received treatment A while the control group received placebo. Results showed a 20% improvement in the treatment group compared to 15% in the control group (p < 0.05)."
      result[:visualAbstract] = "https://via.placeholder.com/600x400/0066cc/ffffff?text=Test+Visual+Abstract"
    else
      # Makale verisi
      content_response = json_rpc_call('extract_jama_article', url)
      if content_response && content_response.success?
        content_data = JSON.parse(content_response.body)
        result[:content] = content_data.dig("result", "data") || content_data["result"]
      else
        puts "Makale içeriği hatası: #{content_response&.code} - #{content_response&.body}"
      end

      # Görsel varsa al
      visual_response = json_rpc_call('get_article_visual', url)
      if visual_response && visual_response.success?
        visual_data = JSON.parse(visual_response.body)
        result[:visualAbstract] = visual_data.dig("result", "visual_url") || visual_data["result"]
      else
        puts "Görsel hatası: #{visual_response&.code} - #{visual_response&.body}"
      end

      # En az bir veri yoksa hata
      unless result[:content] || result[:visualAbstract]
        halt 400, { error: "Makale verisi alınamadı veya görsel bulunamadı. MCP servisine bağlanılamıyor olabilir." }.to_json
      end
    end

    status 200
    result.to_json

  rescue JSON::ParserError
    status 400
    { error: 'Geçersiz JSON' }.to_json
  rescue => e
    status 500
    { error: "Sunucu hatası: #{e.message}" }.to_json
  end
end

# Sağlık kontrolü
get '/api/health' do
  content_type :json
  { status: 'ok' }.to_json
end

# Test endpoint
get '/api/test' do
  content_type :json
  {
    content: "This is a test article content for demonstration purposes. The study included 500 participants with a mean age of 60 years. The intervention group received treatment A while the control group received placebo. Results showed a 20% improvement in the treatment group compared to 15% in the control group (p < 0.05).",
    visualAbstract: "https://via.placeholder.com/600x400/0066cc/ffffff?text=Test+Visual+Abstract"
  }.to_json
end

# Ana sayfa
get '/' do
  content_type :json
  {
    status: 'ok',
    message: 'JAMA Visual Abstract API',
    endpoints: ['/api/process-article', '/api/health', '/api/test']
  }.to_json
end
