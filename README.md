# JAMA Visual Abstract Generator

Bu proje, JAMA makalelerinden görsel abstract oluşturan bir web uygulamasıdır. Smithery MCP (Model Context Protocol) kullanarak makale içeriğini çeker ve görsel abstract'ları analiz eder.

## Özellikler

- **Makale İçeriği Çekme**: MCP ile JAMA makalelerinin içeriğini otomatik olarak çeker
- **Görsel Abstract Çıkarma**: Makalelerdeki mevcut görsel abstract'ları tespit eder
- **Otomatik Görsel Abstract Oluşturma**: Makale içeriğini analiz ederek yeni görsel abstract'lar oluşturur
- **Yan <PERSON>tı<PERSON>**: Orijinal ve oluşturulan görsel abstract'ları yan yana gösterir

## Teknolojiler

### Backend
- **Ruby Sinatra**: Web API
- **HTTParty**: HTTP istekleri için
- **Smithery MCP**: Makale verisi çekme

### Frontend
- **Next.js 15**: React framework
- **TypeScript**: Tip gü<PERSON>ği
- **Tailwind CSS**: Styling
- **Recharts**: Grafik oluşturma
- **Lucide React**: İkonlar

## Kurulum

### Gereksinimler
- Node.js 18+ 
- Ruby 3.0+
- Bundler gem

### Backend Kurulumu

```bash
cd backend
bundle install
./start.sh
```

Backend http://localhost:4567 adresinde çalışacaktır.

### Frontend Kurulumu

```bash
cd frontend
npm install
./start.sh
```

Frontend http://localhost:3000 adresinde çalışacaktır.

## Kullanım

1. Frontend'i açın (http://localhost:3000)
2. JAMA makale URL'sini girin
3. "Makaleyi İşle" butonuna tıklayın
4. Sonuçları görüntüleyin:
   - **Orijinal Görsel Abstract**: Makalede varsa gösterilir
   - **Oluşturulan Görsel Abstract**: Makale içeriğinden oluşturulur

## Desteklenen Makale Formatları

- JAMA Network makaleleri
- JAMA Internal Medicine makaleleri
- JAMA Surgery makaleleri
- Diğer JAMA dergileri

## API Endpoints

### POST /api/process-article
Makale URL'sini alır ve içerik/görsel abstract döndürür.

**Request:**
```json
{
  "url": "https://jamanetwork.com/journals/jama/fullarticle/..."
}
```

**Response:**
```json
{
  "content": "Makale içeriği...",
  "visualAbstract": "https://example.com/image.jpg"
}
```

### GET /api/health
Servis durumunu kontrol eder.

## MCP Entegrasyonu

Proje, Smithery MCP kullanarak makale verilerini çeker:

- **scrape_article**: Makale içeriğini çeker
- **extract_visual_abstract**: Görsel abstract'ı çıkarır

MCP Endpoint: `https://server.smithery.ai/@aliemregencer/jama-abstract-mcp/mcp`

## Hata Yönetimi

- Bağlantı hataları
- Zaman aşımı (30 saniye)
- Geçersiz URL'ler
- MCP hataları
- Makale bulunamama durumları

## Geliştirme

### Backend Geliştirme
```bash
cd backend
bundle exec ruby app.rb
```

### Frontend Geliştirme
```bash
cd frontend
npm run dev
```

## Lisans

MIT License 